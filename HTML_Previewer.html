<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced HTML Previewer Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Fira+Code&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="styles/app.css" rel="stylesheet" />
</head>
<body class="bg-slate-50 dark:bg-slate-900 text-slate-900 dark:text-slate-50">
    <div id="app" class="flex h-screen w-screen text-sm">
        <!-- Files Pane -->
        <div id="files-pane" class="pane bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col" style="width: 300px; min-width: 220px; max-width: 50vw;">
            <div class="pane-content">
                <div class="p-2 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center">
                    <h2 class="font-bold text-base">Project Explorer</h2>
                    <div class="flex items-center gap-0.5">
                        <button id="new-file-btn" title="New File (Ctrl+N)" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path></svg>
                        </button>
                        <button id="new-folder-btn" title="New Folder" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>
                        </button>
                        <button id="import-file-btn" title="Import File" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg>
                        </button>
                    </div>
                </div>
                
                <div class="p-2">
                    <button id="select-folder-btn" class="w-full px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition text-sm">
                        Select Project Folder
                    </button>
                    <input type="file" id="folder-input" webkitdirectory directory multiple style="display: none;" />
                    <input type="file" id="import-input" accept=".html,.htm" style="display: none;" />
                </div>
                
                <div id="file-tree" class="flex-1 overflow-y-auto px-2 py-1">
                    <p class="text-slate-400 text-center py-3 px-2 text-sm">Select a folder to begin or create a new file.</p>
                </div>
                
                <div class="p-2 border-t border-slate-200 dark:border-slate-700">
                    <div id="root-path" class="text-xs text-slate-500 dark:text-slate-400 truncate mb-1.5">No project loaded</div>
                    <button id="save-all-btn" class="w-full px-3 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 transition text-sm disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        Save All (Ctrl+Shift+S)
                    </button>
                </div>
            </div>
            
            <button id="files-toggle" class="pane-toggle absolute top-2 right-2 p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition" title="Toggle Files Panel">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
        </div>

        <!-- Resizer between Files and Code -->
        <div id="resizer-files-code" class="resizer w-1 bg-slate-200 dark:bg-slate-700 hover:bg-blue-500 cursor-col-resize flex-shrink-0" tabindex="0" role="separator" aria-label="Resize files panel"></div>

        <!-- Code Pane -->
        <div id="code-pane" class="pane bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col flex-1">
            <div class="pane-content">
                <div class="p-2 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center">
                    <div class="flex items-center gap-2">
                        <h2 class="font-bold text-base">Code Editor</h2>
                        <div id="file-status-indicator" class="text-slate-400"></div>
                        <span id="file-status" class="text-sm text-slate-600 dark:text-slate-400">No file selected</span>
                    </div>
                    <div class="flex items-center gap-1">
                        <button id="undo-btn" title="Undo (Ctrl+Z)" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                            </svg>
                        </button>
                        <button id="redo-btn" title="Redo (Ctrl+Y)" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6"></path>
                            </svg>
                        </button>
                        <button id="save-btn" title="Save File (Ctrl+S)" class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition text-xs">
                            Save
                        </button>
                        <button id="open-search-btn" title="Search & Replace (Ctrl+F)" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                        <button id="format-btn" title="Format HTML (Ctrl+Shift+F)" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                            </svg>
                        </button>
                        <button id="line-numbers-toggle" title="Toggle Line Numbers" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h18M3 8h18M3 12h18M3 16h18M3 20h18"></path>
                            </svg>
                        </button>
                        <button id="validate-btn" title="Validate HTML" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </button>
                        <button id="dark-mode-toggle" title="Toggle Dark Mode" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div class="editor-container flex-1 relative">
                    <div id="line-numbers" class="absolute left-0 top-0 bottom-0 w-12 bg-slate-100 dark:bg-slate-700 border-r border-slate-200 dark:border-slate-600 text-xs text-slate-500 dark:text-slate-400 font-mono leading-6 p-2 overflow-hidden select-none hidden"></div>
                    <textarea id="html-input" spellcheck="false" placeholder="Start typing your HTML here..."></textarea>
                    <pre id="syntax-highlight"><code class="language-markup"></code></pre>
                    <div id="autocomplete-popup" class="absolute bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded shadow-lg max-h-48 overflow-y-auto z-10 hidden">
                        <div id="autocomplete-list" class="text-sm"></div>
                    </div>
                </div>

                <div id="validation-panel" class="h-32 bg-slate-50 dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 p-2 overflow-y-auto text-xs hidden">
                    <div class="font-bold text-slate-600 dark:text-slate-400 mb-2">HTML Validation Results</div>
                    <div id="validation-results"></div>
                </div>
            </div>
            
            <button id="code-toggle" class="pane-toggle absolute top-2 right-2 p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition" title="Toggle Code Panel">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
        </div>

        <!-- Resizer between Code and Preview -->
        <div id="resizer-code-preview" class="resizer w-1 bg-slate-200 dark:bg-slate-700 hover:bg-blue-500 cursor-col-resize flex-shrink-0" tabindex="0" role="separator" aria-label="Resize code panel"></div>

        <!-- Preview Pane -->
        <div id="preview-pane" class="pane bg-white dark:bg-slate-800 flex flex-col flex-1">
            <div class="pane-content">
                <div class="p-2 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center">
                    <h2 class="font-bold text-base">Live Preview</h2>
                    <div class="flex items-center gap-1">
                        <button id="refresh-preview-btn" title="Refresh Preview" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                        <button id="toggle-console-btn" title="Toggle Console" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <iframe id="preview-output" class="flex-1 bg-white" sandbox="allow-scripts allow-same-origin"></iframe>
                <div id="console-output-container" class="h-32 bg-slate-900 text-white font-mono text-xs p-2 overflow-y-auto border-t border-slate-700 hidden">
                    <div class="font-bold text-slate-400 border-b border-slate-700 mb-1 pb-1">Preview Console</div>
                    <div id="console-output"></div>
                </div>
            </div>
            
            <button id="preview-toggle" class="pane-toggle absolute top-2 right-2 p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition" title="Toggle Preview Panel">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Context Menu -->
    <div id="context-menu" class="fixed bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded shadow-lg py-1 z-50 hidden">
        <button data-action="open" class="w-full px-3 py-1.5 text-left hover:bg-slate-100 dark:hover:bg-slate-700 text-sm">Open</button>
        <button data-action="rename" class="w-full px-3 py-1.5 text-left hover:bg-slate-100 dark:hover:bg-slate-700 text-sm">Rename</button>
        <button data-action="delete" class="w-full px-3 py-1.5 text-left hover:bg-slate-100 dark:hover:bg-slate-700 text-sm text-red-600">Delete</button>
    </div>

    <!-- New File Modal -->
    <div id="new-file-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-slate-800 rounded-lg p-6 w-96">
            <h3 class="text-lg font-bold mb-4">Create New File</h3>
            <input type="text" id="new-file-name" placeholder="Enter file name..." class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded mb-4 bg-white dark:bg-slate-700">
            <div class="flex justify-end gap-2">
                <button id="cancel-new-file" class="px-4 py-2 text-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 rounded">Cancel</button>
                <button id="create-new-file" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Create</button>
            </div>
        </div>
    </div>

    <!-- New Folder Modal -->
    <div id="new-folder-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-slate-800 rounded-lg p-6 w-96">
            <h3 class="text-lg font-bold mb-4">Create New Folder</h3>
            <input type="text" id="new-folder-name" placeholder="Enter folder name..." class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded mb-4 bg-white dark:bg-slate-700">
            <div class="flex justify-end gap-2">
                <button id="cancel-new-folder" class="px-4 py-2 text-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 rounded">Cancel</button>
                <button id="create-new-folder" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Create</button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-confirm-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-slate-800 rounded-lg p-6 w-96">
            <h3 class="text-lg font-bold mb-4 text-red-600">Confirm Delete</h3>
            <p id="delete-modal-message" class="mb-4 text-slate-700 dark:text-slate-300"></p>
            <div class="flex justify-end gap-2">
                <button id="delete-cancel-btn" class="px-4 py-2 text-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 rounded">Cancel</button>
                <button id="delete-confirm-btn" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Delete</button>
            </div>
        </div>
    </div>

    <!-- Search & Replace Modal -->
    <div id="search-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-slate-800 rounded-lg p-6 w-[600px] max-h-[80vh] flex flex-col">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">Search & Replace</h3>
                <button id="search-cancel" class="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-3 mb-4">
                <input type="text" id="search-query" placeholder="Search for..." class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded bg-white dark:bg-slate-700">
                <input type="text" id="search-replace" placeholder="Replace with..." class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded bg-white dark:bg-slate-700">
                
                <div class="flex flex-wrap gap-3 text-sm">
                    <label class="flex items-center gap-1">
                        <input type="checkbox" id="search-regex"> Regex
                    </label>
                    <label class="flex items-center gap-1">
                        <input type="checkbox" id="search-case"> Match Case
                    </label>
                    <label class="flex items-center gap-1">
                        <input type="checkbox" id="search-whole"> Whole Word
                    </label>
                    <label class="flex items-center gap-1">
                        <input type="checkbox" id="search-in-open-only"> Current File Only
                    </label>
                </div>
            </div>
            
            <div class="flex gap-2 mb-4">
                <button id="search-run" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Search</button>
                <button id="search-replace-one" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">Replace Selected</button>
                <button id="search-replace-all" class="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700">Replace All</button>
            </div>
            
            <div class="flex-1 overflow-hidden">
                <div id="search-results" class="max-h-56 overflow-y-auto border border-slate-200 dark:border-slate-700 rounded p-2 bg-white dark:bg-slate-800 text-xs"></div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-markup.min.js"></script>
    <!-- JSZip for proper ZIP creation in Save All -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js" integrity="sha512-0Jxv3dBC1wF8I5CwQO3Yp1Wq2p3lTg8b8wTj1H0kqLk0pJvXr7mJ7w8g7D3t4Wzj5e8xXhQmJvY0G3d0wq8d0w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="scripts/app.js"></script>
</body>
</html>
