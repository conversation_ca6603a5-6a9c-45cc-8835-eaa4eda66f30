:root {
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --font-sans: 'Inter', sans-serif;
    --font-mono: 'Fira Code', monospace;
}
html.dark {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --border-color: #334155;
}
body {
    font-family: var(--font-sans);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    margin: 0;
    padding: 0;
}

#app {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}
.pane-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
}

.editor-container {
    position: relative;
    flex: 1;
    overflow: hidden;
}

/* Line numbers styling */
.editor-container.show-line-numbers #html-input,
.editor-container.show-line-numbers #syntax-highlight {
    padding-left: 3.5rem; /* 56px - space for line numbers */
}

.editor-container.show-line-numbers #line-numbers {
    display: block !important;
}

#line-numbers {
    z-index: 2;
    white-space: pre;
    user-select: none;
    pointer-events: none;
}

/* Autocomplete styling */
#autocomplete-popup {
    z-index: 1000;
    min-width: 200px;
    max-width: 300px;
}

.autocomplete-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.15s ease;
}

.autocomplete-item:last-child {
    border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
    background-color: #3b82f6;
    color: white;
}

.autocomplete-item .tag-name {
    font-weight: 600;
    font-family: var(--font-mono);
}

.autocomplete-item .tag-description {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 2px;
}
#html-input, #syntax-highlight {
    font-family: var(--font-mono);
    font-size: 14px;
    line-height: 1.5;
    /* Both elements use absolute positioning with inset-0 from HTML */
    padding: 1rem;
    margin: 0;
    border: none;
    overflow: auto;
    /* Ensure identical box model */
    box-sizing: border-box;
    /* Ensure identical text rendering */
    white-space: pre-wrap;
    word-wrap: break-word;
    /* Reset any browser defaults */
    outline: none;
    text-align: left;
    vertical-align: top;
    /* Ensure identical scrolling behavior */
    overflow-x: auto;
    overflow-y: auto;
    /* Force identical font rendering */
    font-variant-ligatures: none;
    font-feature-settings: normal;
    text-rendering: optimizeSpeed;
    /* Ensure identical spacing */
    letter-spacing: 0;
    word-spacing: 0;
    /* Prevent any text transformation */
    text-transform: none;
    text-indent: 0;
    /* Ensure consistent tab size */
    tab-size: 4;
    -moz-tab-size: 4;
    /* Force identical line height calculation */
    line-height: 21px; /* Fixed pixel value instead of relative */
    /* Ensure identical font metrics */
    font-weight: 400;
    font-style: normal;
    font-stretch: normal;
    /* Prevent any browser-specific adjustments */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
#html-input {
    resize: none;
    background: transparent;
    color: transparent;
    caret-color: var(--text-primary);
    z-index: 1;
    /* Additional textarea-specific resets */
    border-radius: 0;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    /* Force exact positioning */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
#syntax-highlight {
    pointer-events: none;
    z-index: 0;
    /* Ensure pre element behaves like textarea */
    white-space: pre-wrap;
    word-wrap: break-word;
    /* Force exact positioning to match textarea */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
.toast {
    animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-in 3.7s forwards;
}
@keyframes slideIn { from { transform: translateY(-100%); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
@keyframes fadeOut { from { opacity: 1; } to { opacity: 0; } }
.drag-over {
    outline: 2px dashed #3b82f6;
    outline-offset: -4px;
}

/* Enhanced Pane Management */
.pane {
    transition: all 0.3s ease-in-out;
    position: relative;
    flex-shrink: 0;
}

/* Draggable resizer between panes */
.resizer {
    width: 6px;
    cursor: col-resize;
    background: transparent;
    flex-shrink: 0;
    position: relative;
    transition: background-color 0.15s ease-in-out;
}
.resizer:hover,
.resizer.is-dragging {
    background: rgba(59,130,246,0.35);
}
.pane.collapsed {
    width: 32px !important;
    min-width: 32px !important;
    max-width: 32px !important;
    flex-shrink: 0;
    flex-grow: 0;
    overflow: visible; /* Changed to visible so toggle button shows */
}
.pane.collapsed .pane-content {
    opacity: 0;
    pointer-events: none;
    overflow: hidden;
}
.pane:not(.collapsed) {
    flex-grow: 1; /* Allow non-collapsed panes to grow and fill space */
}
.pane-toggle {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 100;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.pane-toggle:hover {
    background: var(--bg-primary);
    border-color: #3b82f6;
    box-shadow: 0 4px 8px rgba(59,130,246,0.25);
}
.pane-toggle svg {
    transition: transform 0.3s ease;
    width: 14px;
    height: 14px;
    color: #334155;
}
html.dark .pane-toggle svg {
    color: #e2e8f0;
}

/* Ensure collapsed panes don't interfere with layout */
.pane.collapsed {
    border-right-width: 0;
    border-left-width: 0;
}
.pane.collapsed .pane-toggle {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
}
.pane-toggle::after {
    content: "";
    position: absolute;
    inset: -2px;
    border-radius: 6px;
    box-shadow: 0 0 0 1px rgba(59,130,246,0.0);
    transition: box-shadow 0.2s ease;
}
.pane-toggle:hover::after {
    box-shadow: 0 0 0 1px rgba(59,130,246,0.35);
}

/* Files pane specific handling */
#files-pane:not(.collapsed) {
    flex-grow: 0; /* Files pane shouldn't grow, it has fixed width */
    flex-shrink: 0;
}

/* Code and Preview panes share available space */
#code-pane:not(.collapsed),
#preview-pane:not(.collapsed) {
    flex: 1 1 0; /* grow, shrink, basis */
    min-width: 250px; /* Minimum usable width */
}

/* File tree enhancements */
.file-item, .folder-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 3px 6px;
    border-radius: 3px;
    cursor: pointer;
    user-select: none;
    font-size: 13px;
    line-height: 1.3;
}
.file-item:hover, .folder-item:hover {
    background: var(--bg-primary);
}
.file-item.active {
    background: #3b82f6;
    color: white;
}
.file-item.modified::after {
    content: '●';
    color: #f59e0b;
    margin-left: auto;
}

/* Tree structure */
.tree-item {
    display: flex;            /* lay icon + label horizontally */
    align-items: center;      /* vertical center */
    gap: 6px;                 /* spacing between icon and label */
}
.tree-children {
    margin-left: 16px;   /* cleaner indent, remove vertical guide line */
    border-left: none;
    padding-left: 0;
}
.tree-children.collapsed {
    display: none;
}

/* Folder expand/collapse */
.folder-toggle {
    width: 14px;
    height: 14px;
    margin-right: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
    flex-shrink: 0;
}
/* Keep labels on same line and truncate nicely */
.tree-item > span.truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1 1 auto;
}
/* Ensure SVG icons don't shrink oddly */
.tree-item svg {
    flex-shrink: 0;
}
.folder-toggle.expanded {
    transform: rotate(90deg);
}
.folder-toggle svg {
    width: 10px;
    height: 10px;
}

/* Indentation for nested items */
.tree-item[data-depth="1"] { margin-left: 0px; }
.tree-item[data-depth="2"] { margin-left: 12px; }
.tree-item[data-depth="3"] { margin-left: 24px; }
.tree-item[data-depth="4"] { margin-left: 36px; }
.tree-item[data-depth="5"] { margin-left: 48px; }

/* Context menu */
.context-menu {
    position: fixed;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 180px;
}
.context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}
.context-menu-item:hover {
    background: var(--bg-primary);
}
.context-menu-separator {
    height: 1px;
    background: var(--border-color);
    margin: 4px 0;
}

/* Modal styles */
.modal {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}
.modal-content {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 24px;
    min-width: 400px;
    max-width: 90vw;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}
.status-indicator.saved { background: #10b981; }
.status-indicator.modified { background: #f59e0b; }
.status-indicator.error { background: #ef4444; }

/* Enhanced file tree styling for compactness */
#file-tree {
    font-size: 13px;
    line-height: 1.2;
}

/* Improved file tree scrollbar */
#file-tree::-webkit-scrollbar {
    width: 4px;
}
#file-tree::-webkit-scrollbar-track {
    background: transparent;
}
#file-tree::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}
#file-tree::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}
